#!/usr/bin/env python3
"""
测试3阶球谐系数支持的脚本
"""

import os
import sys
import tempfile
import numpy as np
from common import read_ply_file, write_ply_file, read_gaussian_file, write_gaussian_file
from point import Point

def create_test_ply_with_sh(file_path: str, num_points: int = 10):
    """
    创建包含完整3阶球谐系数的测试PLY文件
    """
    try:
        from plyfile import PlyData, PlyElement
    except ImportError:
        print("需要安装 plyfile 库")
        return False
    
    # 生成测试数据
    np.random.seed(42)
    
    vertex_data = []
    for i in range(num_points):
        # 基础属性
        x, y, z = np.random.uniform(-1, 1, 3)
        scale_0, scale_1, scale_2 = np.random.uniform(-2, 0, 3)
        
        # 旋转四元数
        rot_w, rot_x, rot_y, rot_z = np.random.uniform(-1, 1, 4)
        norm = np.sqrt(rot_w**2 + rot_x**2 + rot_y**2 + rot_z**2)
        rot_w, rot_x, rot_y, rot_z = rot_w/norm, rot_x/norm, rot_y/norm, rot_z/norm
        
        opacity = np.random.uniform(-2, 2)
        
        # 球谐系数
        # DC分量 (0阶)
        f_dc_0, f_dc_1, f_dc_2 = np.random.uniform(-0.5, 0.5, 3)
        
        # 高阶分量 (1-3阶)
        f_rest = np.random.uniform(-0.1, 0.1, 45)
        
        # 构建完整的顶点数据
        vertex_tuple = (
            x, y, z,
            scale_0, scale_1, scale_2,
            rot_w, rot_x, rot_y, rot_z,
            opacity,
            f_dc_0, f_dc_1, f_dc_2
        )
        # 添加高阶球谐系数
        vertex_tuple += tuple(f_rest)
        
        vertex_data.append(vertex_tuple)
    
    # 创建数据类型定义
    dtype_list = [
        ('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
        ('scale_0', 'f4'), ('scale_1', 'f4'), ('scale_2', 'f4'),
        ('rot_0', 'f4'), ('rot_1', 'f4'), ('rot_2', 'f4'), ('rot_3', 'f4'),
        ('opacity', 'f4'),
        ('f_dc_0', 'f4'), ('f_dc_1', 'f4'), ('f_dc_2', 'f4')
    ]
    # 添加高阶球谐系数
    for j in range(45):
        dtype_list.append((f'f_rest_{j}', 'f4'))
    
    # 创建结构化数组
    vertex = np.array(vertex_data, dtype=dtype_list)
    
    # 创建PLY元素
    el = PlyElement.describe(vertex, 'vertex')
    
    # 写入PLY文件
    PlyData([el], text=False).write(file_path)
    return True

def test_spherical_harmonics_support():
    """
    测试3阶球谐系数支持
    """
    print("测试3阶球谐系数支持...")
    
    # 检查依赖
    try:
        import plyfile
        print("✓ plyfile 库已安装")
    except ImportError:
        print("✗ 需要安装 plyfile 库: pip install plyfile")
        return False
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建包含完整球谐系数的测试文件
        test_file = os.path.join(temp_dir, "test_sh.ply")
        if not create_test_ply_with_sh(test_file, 5):
            print("✗ 创建测试文件失败")
            return False
        
        print(f"✓ 创建测试PLY文件: {test_file}")
        
        # 测试读取
        try:
            points = read_ply_file(test_file)
            print(f"✓ 成功读取 {len(points)} 个点")
            
            # 检查球谐系数
            first_point = points[0]
            if hasattr(first_point, 'sh_coeffs') and len(first_point.sh_coeffs) > 0:
                print(f"✓ 球谐系数读取成功: {len(first_point.sh_coeffs)} 个系数")
                
                # 显示系数分布
                sh_coeffs = first_point.sh_coeffs
                print(f"  DC分量 (0阶): {sh_coeffs[0]:.4f}, {sh_coeffs[1]:.4f}, {sh_coeffs[2]:.4f}")
                if len(sh_coeffs) >= 12:
                    print(f"  1阶分量示例: {sh_coeffs[3]:.4f}, {sh_coeffs[4]:.4f}, {sh_coeffs[5]:.4f}")
                if len(sh_coeffs) >= 27:
                    print(f"  2阶分量示例: {sh_coeffs[12]:.4f}, {sh_coeffs[13]:.4f}, {sh_coeffs[14]:.4f}")
                if len(sh_coeffs) >= 48:
                    print(f"  3阶分量示例: {sh_coeffs[27]:.4f}, {sh_coeffs[28]:.4f}, {sh_coeffs[29]:.4f}")
                    print(f"✓ 完整的3阶球谐系数 (48个系数)")
                else:
                    print(f"⚠ 球谐系数不完整: {len(sh_coeffs)}/48")
            else:
                print("✗ 未找到球谐系数")
                return False
            
        except Exception as e:
            print(f"✗ 读取测试失败: {e}")
            return False
        
        # 测试写入
        try:
            output_file = os.path.join(temp_dir, "output_sh.ply")
            write_ply_file(output_file, points)
            print(f"✓ 成功写入PLY文件: {output_file}")
            
            # 验证写入的文件
            points2 = read_ply_file(output_file)
            if len(points) == len(points2):
                print(f"✓ 读写一致性验证通过: {len(points)} == {len(points2)}")
                
                # 验证球谐系数保持
                if (hasattr(points2[0], 'sh_coeffs') and 
                    len(points2[0].sh_coeffs) == len(points[0].sh_coeffs)):
                    print("✓ 球谐系数完整性保持")
                    
                    # 检查数值精度
                    max_diff = max(abs(a - b) for a, b in zip(points[0].sh_coeffs, points2[0].sh_coeffs))
                    if max_diff < 1e-6:
                        print(f"✓ 球谐系数精度保持 (最大差异: {max_diff:.2e})")
                    else:
                        print(f"⚠ 球谐系数精度损失 (最大差异: {max_diff:.2e})")
                else:
                    print("✗ 球谐系数完整性丢失")
                    return False
            else:
                print(f"✗ 读写一致性验证失败: {len(points)} != {len(points2)}")
                return False
                
        except Exception as e:
            print(f"✗ 写入测试失败: {e}")
            return False
    
    return True

def test_backward_compatibility():
    """
    测试向后兼容性 (不包含高阶球谐系数的PLY文件)
    """
    print("\n测试向后兼容性...")
    
    try:
        from plyfile import PlyData, PlyElement
    except ImportError:
        print("✗ 需要安装 plyfile 库")
        return False
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建只包含DC分量的PLY文件
        test_file = os.path.join(temp_dir, "test_dc_only.ply")
        
        vertex_data = [(0.0, 0.0, 0.0, -1.0, -1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.1, 0.2, 0.3)]
        vertex = np.array(vertex_data, dtype=[
            ('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
            ('scale_0', 'f4'), ('scale_1', 'f4'), ('scale_2', 'f4'),
            ('rot_0', 'f4'), ('rot_1', 'f4'), ('rot_2', 'f4'), ('rot_3', 'f4'),
            ('opacity', 'f4'),
            ('f_dc_0', 'f4'), ('f_dc_1', 'f4'), ('f_dc_2', 'f4')
        ])
        
        el = PlyElement.describe(vertex, 'vertex')
        PlyData([el], text=False).write(test_file)
        
        # 测试读取
        try:
            points = read_ply_file(test_file)
            print(f"✓ 向后兼容性测试通过: 读取 {len(points)} 个点")
            
            first_point = points[0]
            if hasattr(first_point, 'sh_coeffs'):
                print(f"✓ 球谐系数数组创建: {len(first_point.sh_coeffs)} 个系数")
                if len(first_point.sh_coeffs) >= 3:
                    print(f"  DC分量: {first_point.sh_coeffs[0]:.3f}, {first_point.sh_coeffs[1]:.3f}, {first_point.sh_coeffs[2]:.3f}")
                    print("✓ DC分量正确读取")
                if len(first_point.sh_coeffs) == 48:
                    print("✓ 高阶系数自动填充为0")
                    return True
            else:
                print("✗ 球谐系数数组未创建")
                return False
                
        except Exception as e:
            print(f"✗ 向后兼容性测试失败: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("3阶球谐系数支持测试")
    print("=" * 50)
    
    success1 = test_spherical_harmonics_support()
    success2 = test_backward_compatibility()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✓ 3阶球谐系数支持测试全部通过")
        print("\n新功能:")
        print("- ✅ 完整的3阶球谐系数支持 (48个系数)")
        print("- ✅ DC分量 + 1-3阶高阶分量")
        print("- ✅ 向后兼容性保持")
        print("- ✅ 格式保持功能")
        print("\n现在支持完整的视角相关颜色变化!")
    else:
        print("✗ 部分测试失败，请检查错误信息")
