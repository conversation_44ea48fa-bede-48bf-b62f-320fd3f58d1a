#!/usr/bin/env python3
"""
测试格式保持功能的脚本
验证split和build lod阶段是否能保持输入文件格式
"""

import os
import sys
import tempfile
import shutil
from common import read_gaussian_file, write_gaussian_file, get_point_num
from point import Point

def create_test_ply_file(file_path: str, num_points: int = 100):
    """
    创建一个测试用的PLY文件
    """
    try:
        import numpy as np
        from plyfile import PlyData, PlyElement
    except ImportError:
        print("需要安装 plyfile 和 numpy 库")
        return False
    
    # 生成随机测试数据
    np.random.seed(42)  # 固定随机种子以便重现
    
    vertex_data = []
    for i in range(num_points):
        # 位置 (在一个小范围内)
        x = np.random.uniform(-10, 10)
        y = np.random.uniform(-10, 10)
        z = np.random.uniform(-1, 1)
        
        # 缩放 (对数空间)
        scale_0 = np.random.uniform(-2, 0)  # exp(-2) to exp(0) = 0.135 to 1.0
        scale_1 = np.random.uniform(-2, 0)
        scale_2 = np.random.uniform(-2, 0)
        
        # 旋转四元数 (归一化)
        rot_w = np.random.uniform(-1, 1)
        rot_x = np.random.uniform(-1, 1)
        rot_y = np.random.uniform(-1, 1)
        rot_z = np.random.uniform(-1, 1)
        norm = np.sqrt(rot_w**2 + rot_x**2 + rot_y**2 + rot_z**2)
        rot_w /= norm
        rot_x /= norm
        rot_y /= norm
        rot_z /= norm
        
        # 透明度 (logit空间)
        opacity = np.random.uniform(-2, 2)
        
        # 颜色 (球谐系数DC分量)
        f_dc_0 = np.random.uniform(-0.5, 0.5)
        f_dc_1 = np.random.uniform(-0.5, 0.5)
        f_dc_2 = np.random.uniform(-0.5, 0.5)
        
        vertex_data.append((
            x, y, z,
            scale_0, scale_1, scale_2,
            rot_w, rot_x, rot_y, rot_z,
            opacity,
            f_dc_0, f_dc_1, f_dc_2
        ))
    
    # 创建结构化数组
    vertex = np.array(vertex_data, dtype=[
        ('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
        ('scale_0', 'f4'), ('scale_1', 'f4'), ('scale_2', 'f4'),
        ('rot_0', 'f4'), ('rot_1', 'f4'), ('rot_2', 'f4'), ('rot_3', 'f4'),
        ('opacity', 'f4'),
        ('f_dc_0', 'f4'), ('f_dc_1', 'f4'), ('f_dc_2', 'f4')
    ])
    
    # 创建PLY元素
    el = PlyElement.describe(vertex, 'vertex')
    
    # 写入PLY文件
    PlyData([el], text=False).write(file_path)
    return True

def create_test_splat_file(file_path: str, num_points: int = 100):
    """
    创建一个测试用的SPLAT文件
    """
    import numpy as np
    
    # 生成随机测试数据
    np.random.seed(42)  # 固定随机种子以便重现
    
    points = []
    for i in range(num_points):
        # 位置
        position = (
            np.random.uniform(-10, 10),
            np.random.uniform(-10, 10),
            np.random.uniform(-1, 1)
        )
        
        # 颜色 (RGBA)
        color = (
            int(np.random.uniform(0, 255)),
            int(np.random.uniform(0, 255)),
            int(np.random.uniform(0, 255)),
            int(np.random.uniform(100, 255))  # 确保有一定透明度
        )
        
        # 缩放
        scale = (
            np.random.uniform(0.1, 1.0),
            np.random.uniform(0.1, 1.0),
            np.random.uniform(0.1, 1.0)
        )
        
        # 旋转 (0-255范围的四元数)
        rotation = (
            int(np.random.uniform(0, 255)),
            int(np.random.uniform(0, 255)),
            int(np.random.uniform(0, 255)),
            int(np.random.uniform(0, 255))
        )
        
        points.append(Point(position, color, scale, rotation))
    
    write_gaussian_file(file_path, points)
    return True

def test_format_preservation():
    """
    测试格式保持功能
    """
    print("测试格式保持功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 测试PLY格式保持
        print("\n1. 测试PLY格式保持")
        ply_input_dir = os.path.join(temp_dir, "ply_input")
        os.makedirs(ply_input_dir)
        
        ply_test_file = os.path.join(ply_input_dir, "test.ply")
        if create_test_ply_file(ply_test_file, 50):
            print(f"✓ 创建测试PLY文件: {ply_test_file}")
            
            # 测试读取
            points = read_gaussian_file(ply_test_file)
            print(f"✓ 读取到 {len(points)} 个点")
            
            # 测试写入
            ply_output_file = os.path.join(temp_dir, "output_test.ply")
            write_gaussian_file(ply_output_file, points)
            print(f"✓ 写入PLY文件: {ply_output_file}")
            
            # 验证读写一致性
            points2 = read_gaussian_file(ply_output_file)
            if len(points) == len(points2):
                print(f"✓ 读写一致性验证通过: {len(points)} == {len(points2)}")
            else:
                print(f"✗ 读写一致性验证失败: {len(points)} != {len(points2)}")
        else:
            print("✗ 创建测试PLY文件失败")
        
        # 测试SPLAT格式保持
        print("\n2. 测试SPLAT格式保持")
        splat_input_dir = os.path.join(temp_dir, "splat_input")
        os.makedirs(splat_input_dir)
        
        splat_test_file = os.path.join(splat_input_dir, "test.splat")
        if create_test_splat_file(splat_test_file, 50):
            print(f"✓ 创建测试SPLAT文件: {splat_test_file}")
            
            # 测试读取
            points = read_gaussian_file(splat_test_file)
            print(f"✓ 读取到 {len(points)} 个点")
            
            # 测试写入
            splat_output_file = os.path.join(temp_dir, "output_test.splat")
            write_gaussian_file(splat_output_file, points)
            print(f"✓ 写入SPLAT文件: {splat_output_file}")
            
            # 验证读写一致性
            points2 = read_gaussian_file(splat_output_file)
            if len(points) == len(points2):
                print(f"✓ 读写一致性验证通过: {len(points)} == {len(points2)}")
            else:
                print(f"✗ 读写一致性验证失败: {len(points)} != {len(points2)}")
        else:
            print("✗ 创建测试SPLAT文件失败")
        
        # 测试格式自动检测
        print("\n3. 测试格式自动检测")
        test_files = [
            (ply_test_file, "PLY"),
            (splat_test_file, "SPLAT")
        ]
        
        for test_file, format_name in test_files:
            if os.path.exists(test_file):
                try:
                    points = read_gaussian_file(test_file)
                    print(f"✓ {format_name}格式自动检测成功: {len(points)} 个点")
                except Exception as e:
                    print(f"✗ {format_name}格式自动检测失败: {e}")
    
    print("\n格式保持功能测试完成")

if __name__ == "__main__":
    print("格式保持功能测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import numpy as np
        print("✓ numpy 已安装")
    except ImportError:
        print("✗ 需要安装 numpy: pip install numpy")
        sys.exit(1)
    
    try:
        import plyfile
        print("✓ plyfile 已安装")
    except ImportError:
        print("✗ 需要安装 plyfile: pip install plyfile")
        sys.exit(1)
    
    test_format_preservation()
    
    print("\n" + "=" * 50)
    print("✓ 格式保持功能已实现")
    print("\n现在split和build lod阶段将保持输入文件格式:")
    print("- 输入PLY文件 → 输出PLY文件")
    print("- 输入SPLAT文件 → 输出SPLAT文件")
    print("- 支持混合格式处理")
