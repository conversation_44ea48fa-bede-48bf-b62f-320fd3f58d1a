#!/usr/bin/env python3
"""
测试PLY读取修复
"""

import tempfile
import os
import numpy as np
from common import read_ply_file, write_ply_file
from point import Point

def test_ply_fix():
    """测试PLY读取修复"""
    print("测试PLY读取修复...")
    
    # 创建一个简单的测试点
    test_point = Point(
        position=(1.0, 2.0, 3.0),
        color=(255, 128, 64, 200),
        scale=(0.5, 0.6, 0.7),
        rotation=(128, 64, 192, 32),
        sh_coeffs=[0.1, 0.2, 0.3] + [0.0] * 45  # DC分量 + 45个高阶系数
    )
    
    print(f"原始点: 位置={test_point.position}, 颜色={test_point.color}")
    print(f"球谐系数: {len(test_point.sh_coeffs)}个")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.ply', delete=False) as f:
        temp_file = f.name
    
    try:
        # 测试写入
        print("测试写入...")
        write_ply_file(temp_file, [test_point])
        print("✓ 写入成功")
        
        # 测试读取
        print("测试读取...")
        points = read_ply_file(temp_file)
        print(f"✓ 读取成功: {len(points)}个点")
        
        if points:
            p = points[0]
            print(f"读取的点: 位置={p.position}, 颜色={p.color}")
            if hasattr(p, 'sh_coeffs') and p.sh_coeffs:
                print(f"球谐系数: {len(p.sh_coeffs)}个")
                print(f"DC分量: {p.sh_coeffs[:3]}")
            else:
                print("没有球谐系数")
        
        print("✓ 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_ply_without_sh():
    """测试没有高阶球谐系数的PLY文件"""
    print("\n测试没有高阶球谐系数的PLY文件...")
    
    try:
        from plyfile import PlyData, PlyElement
    except ImportError:
        print("需要安装plyfile库")
        return False
    
    # 创建只有DC分量的PLY文件
    vertex_data = [(1.0, 2.0, 3.0, -1.0, -1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.1, 0.2, 0.3)]
    vertex = np.array(vertex_data, dtype=[
        ('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
        ('scale_0', 'f4'), ('scale_1', 'f4'), ('scale_2', 'f4'),
        ('rot_0', 'f4'), ('rot_1', 'f4'), ('rot_2', 'f4'), ('rot_3', 'f4'),
        ('opacity', 'f4'),
        ('f_dc_0', 'f4'), ('f_dc_1', 'f4'), ('f_dc_2', 'f4')
    ])
    
    with tempfile.NamedTemporaryFile(suffix='.ply', delete=False) as f:
        temp_file = f.name
    
    try:
        # 写入PLY文件
        el = PlyElement.describe(vertex, 'vertex')
        PlyData([el], text=False).write(temp_file)
        print("✓ 创建测试PLY文件")
        
        # 测试读取
        points = read_ply_file(temp_file)
        print(f"✓ 读取成功: {len(points)}个点")
        
        if points:
            p = points[0]
            print(f"读取的点: 位置={p.position}")
            if hasattr(p, 'sh_coeffs') and p.sh_coeffs:
                print(f"球谐系数: {len(p.sh_coeffs)}个")
                print(f"DC分量: {p.sh_coeffs[:3]}")
                # 检查高阶系数是否被填充为0
                if len(p.sh_coeffs) >= 48:
                    high_order = p.sh_coeffs[3:48]
                    if all(x == 0.0 for x in high_order):
                        print("✓ 高阶系数正确填充为0")
                    else:
                        print("⚠ 高阶系数不全为0")
            else:
                print("没有球谐系数")
        
        print("✓ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    print("PLY读取修复测试")
    print("=" * 40)
    
    success1 = test_ply_fix()
    success2 = test_ply_without_sh()
    
    print("\n" + "=" * 40)
    if success1 and success2:
        print("✓ 所有测试通过，PLY读取问题已修复")
        print("\n现在可以正常使用:")
        print("python3 debug_ply_performance.py ./data/demo_24/ply")
        print("python3 main.py --input ./data/demo_24/ply --output ./data/demo_24/3dtiles --enu_origin 118.91 32.12")
    else:
        print("✗ 部分测试失败，请检查错误信息")
