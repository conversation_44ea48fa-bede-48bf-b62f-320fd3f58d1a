#!/usr/bin/env python3
"""
测试自动优化选择功能
"""

import os
import tempfile
import shutil
from main import analyze_tile_complexity
from common import write_gaussian_file
from point import Point

def create_test_tiles(output_dir, scenario="small"):
    """
    创建测试瓦片文件
    """
    os.makedirs(output_dir, exist_ok=True)
    
    if scenario == "small":
        # 小场景: 少量小文件
        for i in range(3):
            points = []
            for j in range(1000):  # 1000个点
                point = Point(
                    position=(j * 0.1, i * 0.1, 0.0),
                    color=(255, 128, 64, 200),
                    scale=(0.1, 0.1, 0.1),
                    rotation=(128, 128, 128, 128)
                )
                points.append(point)
            
            file_path = os.path.join(output_dir, f"tile_{i}.splat")
            write_gaussian_file(file_path, points)
            
    elif scenario == "large":
        # 大场景: 大量大文件
        for i in range(5):
            points = []
            for j in range(200000):  # 20万个点
                point = Point(
                    position=(j * 0.01, i * 0.01, 0.0),
                    color=(255, 128, 64, 200),
                    scale=(0.05, 0.05, 0.05),
                    rotation=(128, 128, 128, 128)
                )
                points.append(point)
            
            # 混合PLY和SPLAT文件
            if i % 2 == 0:
                file_path = os.path.join(output_dir, f"tile_{i}.ply")
            else:
                file_path = os.path.join(output_dir, f"tile_{i}.splat")
            write_gaussian_file(file_path, points)
            
    elif scenario == "mixed":
        # 混合场景: 一些大文件，一些小文件
        for i in range(8):
            if i < 3:
                # 小文件
                point_count = 5000
            else:
                # 大文件
                point_count = 150000
                
            points = []
            for j in range(point_count):
                point = Point(
                    position=(j * 0.01, i * 0.01, 0.0),
                    color=(255, 128, 64, 200),
                    scale=(0.05, 0.05, 0.05),
                    rotation=(128, 128, 128, 128)
                )
                points.append(point)
            
            # 大部分使用PLY格式
            if i > 1:
                file_path = os.path.join(output_dir, f"tile_{i}.ply")
            else:
                file_path = os.path.join(output_dir, f"tile_{i}.splat")
            write_gaussian_file(file_path, points)

def test_auto_optimization():
    """
    测试自动优化选择
    """
    print("测试自动优化选择功能")
    print("=" * 50)
    
    scenarios = [
        ("small", "小场景测试"),
        ("mixed", "混合场景测试"),
        ("large", "大场景测试")
    ]
    
    for scenario, description in scenarios:
        print(f"\n{description}:")
        print("-" * 30)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试数据
            print(f"创建 {scenario} 场景的测试数据...")
            create_test_tiles(temp_dir, scenario)
            
            # 分析复杂度
            try:
                should_use_optimized, estimated_points, total_files, analysis_info = analyze_tile_complexity(temp_dir)
                
                print(f"分析结果:")
                print(f"  文件数量: {analysis_info['total_files']}")
                print(f"  估算点数: {analysis_info['estimated_points']:,}")
                print(f"  文件总大小: {analysis_info['total_size_mb']:.1f}MB")
                print(f"  大文件数量: {analysis_info['large_files']}")
                print(f"  PLY文件数量: {analysis_info['ply_files']}")
                
                if should_use_optimized:
                    print(f"✓ 建议使用优化版本")
                    print(f"  原因:")
                    for reason in analysis_info['reasons']:
                        print(f"    - {reason}")
                else:
                    print(f"✓ 建议使用标准版本")
                
                # 验证决策逻辑
                if scenario == "small" and should_use_optimized:
                    print("⚠️  警告: 小场景不应该使用优化版本")
                elif scenario == "large" and not should_use_optimized:
                    print("⚠️  警告: 大场景应该使用优化版本")
                else:
                    print("✓ 决策逻辑正确")
                    
            except Exception as e:
                print(f"✗ 分析失败: {e}")
                import traceback
                traceback.print_exc()

def test_command_line_options():
    """
    测试命令行选项
    """
    print(f"\n命令行选项测试:")
    print("-" * 30)
    
    print("可用的新选项:")
    print("  --force_optimized    强制使用优化版本")
    print("  --force_standard     强制使用标准版本")
    print()
    print("使用示例:")
    print("  # 自动检测 (默认)")
    print("  python3 main.py --input ./data --output ./output --enu_origin 118.91 32.12")
    print()
    print("  # 强制使用优化版本")
    print("  python3 main.py --input ./data --output ./output --enu_origin 118.91 32.12 --force_optimized")
    print()
    print("  # 强制使用标准版本")
    print("  python3 main.py --input ./data --output ./output --enu_origin 118.91 32.12 --force_standard")

if __name__ == "__main__":
    test_auto_optimization()
    test_command_line_options()
    
    print(f"\n" + "=" * 50)
    print("✓ 自动优化选择功能已实现")
    print()
    print("功能特点:")
    print("- 🔍 自动分析瓦片文件复杂度")
    print("- 🚀 大文件自动使用优化版本")
    print("- 📋 小文件使用标准版本")
    print("- 🔧 支持手动强制选择")
    print("- ⚡ 智能回退机制")
    print()
    print("现在运行 main.py 时会自动选择最适合的处理方式！")
