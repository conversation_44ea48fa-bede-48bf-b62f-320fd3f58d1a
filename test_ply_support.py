#!/usr/bin/env python3
"""
测试PLY格式支持的脚本
"""

import os
import sys
from common import read_ply_file, read_gaussian_file, get_point_num

def test_ply_support():
    """
    测试PLY格式支持功能
    """
    print("测试PLY格式支持...")
    
    # 检查是否安装了plyfile库
    try:
        import plyfile
        print("✓ plyfile库已安装")
    except ImportError:
        print("✗ 需要安装plyfile库: pip install plyfile")
        return False
    
    # 查找测试文件
    test_dirs = ["./data", "./"]
    ply_files = []
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for root, dirs, files in os.walk(test_dir):
                for file in files:
                    if file.endswith('.ply'):
                        ply_files.append(os.path.join(root, file))
    
    if not ply_files:
        print("✗ 未找到PLY测试文件")
        print("请将PLY文件放在./data目录下进行测试")
        return False
    
    print(f"✓ 找到 {len(ply_files)} 个PLY文件")
    
    # 测试第一个PLY文件
    test_file = ply_files[0]
    print(f"测试文件: {test_file}")
    
    try:
        # 测试点数量获取
        point_count = get_point_num(test_file)
        print(f"✓ 点数量: {point_count}")
        
        # 测试文件读取 (只读取前100个点以节省时间)
        if point_count > 0:
            points = read_gaussian_file(test_file)
            if len(points) > 0:
                print(f"✓ 成功读取 {len(points)} 个点")
                
                # 显示第一个点的信息
                first_point = points[0]
                print(f"  第一个点信息:")
                print(f"    位置: {first_point.position}")
                print(f"    颜色: {first_point.color}")
                print(f"    缩放: {first_point.scale}")
                print(f"    旋转: {first_point.rotation}")
                
                return True
            else:
                print("✗ 读取的点数量为0")
                return False
        else:
            print("✗ 文件中没有点数据")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_mixed_format_support():
    """
    测试混合格式支持
    """
    print("\n测试混合格式支持...")
    
    # 查找不同格式的文件
    test_dirs = ["./data"]
    splat_files = []
    ply_files = []
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for root, dirs, files in os.walk(test_dir):
                for file in files:
                    if file.endswith('.splat'):
                        splat_files.append(os.path.join(root, file))
                    elif file.endswith('.ply'):
                        ply_files.append(os.path.join(root, file))
    
    print(f"找到 {len(splat_files)} 个SPLAT文件, {len(ply_files)} 个PLY文件")
    
    # 测试通用读取函数
    all_files = splat_files + ply_files
    if all_files:
        for test_file in all_files[:2]:  # 只测试前两个文件
            try:
                points = read_gaussian_file(test_file)
                print(f"✓ {os.path.basename(test_file)}: {len(points)} 个点")
            except Exception as e:
                print(f"✗ {os.path.basename(test_file)}: {e}")
    else:
        print("✗ 未找到测试文件")

if __name__ == "__main__":
    print("PLY格式支持测试")
    print("=" * 50)
    
    success = test_ply_support()
    test_mixed_format_support()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ PLY格式支持测试通过")
        print("\n现在可以使用以下命令处理PLY文件:")
        print("python main.py --input ./data/your_ply_folder --output ./output --enu_origin 0.0 0.0")
    else:
        print("✗ PLY格式支持测试失败")
        print("请检查错误信息并修复问题")
