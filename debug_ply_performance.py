#!/usr/bin/env python3
"""
PLY文件性能调试脚本
用于诊断PLY文件处理过程中的性能瓶颈
"""

import os
import sys
import time
import gc
from typing import List
from common import read_gaussian_file, get_point_num

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def analyze_ply_file(file_path: str):
    """分析单个PLY文件的性能"""
    print(f"\n分析文件: {os.path.basename(file_path)}")
    print("-" * 50)
    
    # 文件基本信息
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {format_size(file_size)}")
    
    # 测试点数量获取
    start_time = time.time()
    try:
        point_count = get_point_num(file_path)
        count_time = time.time() - start_time
        print(f"点数量: {point_count:,}")
        print(f"点数量获取耗时: {count_time:.3f}秒")
    except Exception as e:
        print(f"获取点数量失败: {e}")
        return False
    
    # 估算内存使用
    estimated_memory = point_count * 100  # 每个点大约100字节
    print(f"估算内存使用: {format_size(estimated_memory)}")
    
    # 检查是否为大文件
    if point_count > 1000000:
        print(f"⚠️  大文件警告: {point_count:,} 个点")
        print("   这可能导致处理缓慢和内存不足")
        
        # 询问是否继续
        response = input("是否继续读取测试? (y/N): ").strip().lower()
        if response != 'y':
            print("跳过读取测试")
            return True
    
    # 测试文件读取
    print("\n开始读取测试...")
    start_time = time.time()
    
    try:
        # 强制垃圾回收
        gc.collect()
        
        points = read_gaussian_file(file_path)
        read_time = time.time() - start_time
        
        print(f"读取完成: {len(points):,} 个点")
        print(f"读取耗时: {read_time:.3f}秒")
        print(f"读取速度: {len(points)/read_time:.0f} 点/秒")
        
        # 检查第一个点的信息
        if points:
            first_point = points[0]
            print(f"\n第一个点信息:")
            print(f"  位置: {first_point.position}")
            print(f"  颜色: {first_point.color}")
            if hasattr(first_point, 'sh_coeffs') and first_point.sh_coeffs:
                print(f"  球谐系数: {len(first_point.sh_coeffs)} 个")
            else:
                print(f"  球谐系数: 无")
        
        # 清理内存
        del points
        gc.collect()
        
        return True
        
    except Exception as e:
        print(f"读取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_directory(directory: str):
    """分析目录中的所有PLY文件"""
    print(f"分析目录: {directory}")
    print("=" * 60)
    
    # 查找PLY文件
    ply_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.ply'):
                ply_files.append(os.path.join(root, file))
    
    if not ply_files:
        print("未找到PLY文件")
        return
    
    print(f"找到 {len(ply_files)} 个PLY文件")
    
    # 按文件大小排序
    ply_files.sort(key=lambda x: os.path.getsize(x))
    
    total_points = 0
    total_size = 0
    successful_files = 0
    
    for i, file_path in enumerate(ply_files):
        print(f"\n[{i+1}/{len(ply_files)}]", end=" ")
        
        try:
            file_size = os.path.getsize(file_path)
            total_size += file_size
            
            if analyze_ply_file(file_path):
                successful_files += 1
                point_count = get_point_num(file_path)
                total_points += point_count
            
        except Exception as e:
            print(f"分析文件失败: {e}")
        
        # 每5个文件后暂停一下
        if (i + 1) % 5 == 0 and i + 1 < len(ply_files):
            response = input(f"\n已分析 {i+1} 个文件，是否继续? (Y/n): ").strip().lower()
            if response == 'n':
                break
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"分析总结:")
    print(f"  总文件数: {len(ply_files)}")
    print(f"  成功分析: {successful_files}")
    print(f"  总文件大小: {format_size(total_size)}")
    print(f"  总点数: {total_points:,}")
    if total_points > 0:
        print(f"  平均文件大小: {format_size(total_size // len(ply_files))}")
        print(f"  平均点数: {total_points // len(ply_files):,}")

def check_system_resources():
    """检查系统资源"""
    print("系统资源检查:")
    print("-" * 30)
    
    # 检查可用内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"总内存: {format_size(memory.total)}")
        print(f"可用内存: {format_size(memory.available)}")
        print(f"内存使用率: {memory.percent:.1f}%")
        
        if memory.available < 2 * 1024 * 1024 * 1024:  # 小于2GB
            print("⚠️  警告: 可用内存不足，可能影响大文件处理")
    except ImportError:
        print("无法获取内存信息 (需要安装psutil)")
    
    # 检查CPU核心数
    import multiprocessing
    cpu_count = multiprocessing.cpu_count()
    print(f"CPU核心数: {cpu_count}")

def main():
    print("PLY文件性能调试工具")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        print("用法: python debug_ply_performance.py <文件或目录路径>")
        print("\n示例:")
        print("  python debug_ply_performance.py ./data/demo_24/ply")
        print("  python debug_ply_performance.py ./data/demo_24/ply/file.ply")
        return
    
    path = sys.argv[1]
    
    if not os.path.exists(path):
        print(f"路径不存在: {path}")
        return
    
    # 检查系统资源
    check_system_resources()
    print()
    
    if os.path.isfile(path):
        # 分析单个文件
        if path.endswith('.ply'):
            analyze_ply_file(path)
        else:
            print("不是PLY文件")
    elif os.path.isdir(path):
        # 分析目录
        analyze_directory(path)
    else:
        print("无效的路径")

if __name__ == "__main__":
    main()
